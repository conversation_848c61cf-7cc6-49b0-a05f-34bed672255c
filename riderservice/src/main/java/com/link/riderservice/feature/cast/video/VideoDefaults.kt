package com.link.riderservice.feature.cast.video

/**
 * 视频相关默认参数与命名集中管理，便于统一配置与测试覆盖。
 */
internal object VideoDefaults {
    // 编码参数
    const val BITRATE: Int = 6 * 1024 * 1024 // 6Mbps
    const val I_FRAME_INTERVAL: Int = 1 // I 帧间隔（秒）
    const val DEFAULT_FRAME_RATE: Int = 30 // 默认帧率
    const val FRAME_INTERVAL_MILLIS: Long = 1000L / DEFAULT_FRAME_RATE

    // 虚拟显示器名称
    const val MIRROR_DISPLAY_NAME: String = "Mirror Display"
    const val PRESENTATION_DISPLAY_NAME: String = "ext-display"

    // 线程名称
    const val CODEC_THREAD_NAME: String = "MediaProjectService"
    const val SCREEN_CAPTURE_THREAD_NAME: String = "ScreenCaptureThread"
}




