package com.link.riderservice.feature.cast.video.codec

import android.media.MediaCodec
import android.media.MediaFormat
import android.view.Surface
import com.link.riderservice.core.logging.logD
import com.link.riderservice.core.logging.logE

/**
 * 负责管理 MediaCodec 编码器的生命周期与回调。
 */
internal class EncoderController(
    private val backgroundHandlerLooperProvider: () -> android.os.Looper
) {

    interface EncodedFrameListener {
        fun onEncodedFrame(data: ByteArray, size: Int, flags: Int)
        fun onOutputFormatChanged(format: MediaFormat) {}
        fun onCodecError(e: MediaCodec.CodecException) {}
    }

    private var mediaCodec: MediaCodec? = null
    private var inputSurface: Surface? = null
    private var encodedFrameListener: EncodedFrameListener? = null

    fun setEncodedFrameListener(listener: EncodedFrameListener) {
        this.encodedFrameListener = listener
    }

    fun isInitialized(): Boolean = mediaCodec != null

    fun configureAndPrepare(mediaFormat: MediaFormat) {
        try {
            if (mediaCodec == null) {
                mediaCodec = MediaCodec.createEncoderByType("video/avc")
            }
            mediaCodec?.apply {
                setCallback(createCodecCallback(), android.os.Handler(backgroundHandlerLooperProvider()))
                configure(mediaFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE)
                inputSurface = createInputSurface()
            }
        } catch (e: Exception) {
            logE(TAG, "Failed to initialize MediaCodec", e)
            release()
            throw e
        }
    }

    fun start() {
        try {
            mediaCodec?.start()
        } catch (e: Exception) {
            logE(TAG, "Failed to start MediaCodec", e)
        }
    }

    fun stop() {
        try {
            mediaCodec?.apply {
                stop()
                release()
            }
        } catch (e: Exception) {
            logE(TAG, "Error stopping MediaCodec", e)
        } finally {
            mediaCodec = null
            try {
                inputSurface?.release()
            } catch (e: Exception) {
                logE(TAG, "Error releasing codec input surface", e)
            }
            inputSurface = null
        }
    }

    fun release() {
        stop()
    }

    fun getInputSurface(): Surface? = inputSurface

    private fun createCodecCallback() = object : MediaCodec.Callback() {
        override fun onInputBufferAvailable(codec: MediaCodec, index: Int) {
            // Surface 输入，不需要处理 input buffer
        }

        override fun onOutputBufferAvailable(codec: MediaCodec, index: Int, info: MediaCodec.BufferInfo) {
            try {
                codec.getOutputBuffer(index)?.let { outputBuffer ->
                    outputBuffer.position(info.offset)
                    outputBuffer.limit(info.offset + info.size)
                    val data = ByteArray(info.size)
                    outputBuffer.get(data, 0, info.size)
                    encodedFrameListener?.onEncodedFrame(data, info.size, info.flags)
                    codec.releaseOutputBuffer(index, false)
                }
            } catch (e: IllegalStateException) {
                logE(TAG, "Error processing encoded frame", e)
            }
        }

        override fun onError(codec: MediaCodec, e: MediaCodec.CodecException) {
            logE(TAG, "MediaCodec error", e)
            encodedFrameListener?.onCodecError(e)
        }

        override fun onOutputFormatChanged(codec: MediaCodec, format: MediaFormat) {
            logD(TAG, "MediaCodec output format changed: $format")
            encodedFrameListener?.onOutputFormatChanged(format)
        }
    }

    companion object {
        private const val TAG = "EncoderController"
    }
}




