package com.link.riderservice.feature.connection.transport

import com.link.riderservice.core.logging.logD
import java.io.IOException

internal abstract class DataConnection protected constructor() {
    interface Callback {
        /**
         * Called when data connection is established.
         */
        fun onConnected(transport: Transport)

        /**
         * Called when data connection is disconnected.
         */
        fun onDisconnected()
        fun requestLockScreenDisplay()
    }

    protected var eventCallback: Callback? = null
    private var isInitialized = true

    /**
     * 关闭连接
     */
    fun shutdown() {
        if (isInitialized) {
            logD(TAG,"Shutting down...")
            isInitialized = false
            eventCallback?.onDisconnected()
            eventCallback = null
            onShutdown()
            logD(TAG,"Shutdown completed.")
        }
    }

    /**
     * start DataConnection.
     * @param callback 回调
     * @throws IOException may be error [IOException]
     * @see Callback
     */
    @Throws(IOException::class)
    fun start(callback: Callback?) {
        logD(TAG,"Starting...")
        eventCallback = callback
        isInitialized = true
        onStart()
        logD(TAG,"Start sequence finished.")
    }

    @Throws(IOException::class)
    protected abstract fun onStart()
    protected abstract fun onShutdown()


    companion object {
        const val TAG = "DataConnection"
    }
}