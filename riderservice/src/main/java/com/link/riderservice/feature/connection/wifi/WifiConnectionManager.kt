package com.link.riderservice.feature.connection.wifi

import android.content.Context
import android.net.wifi.ScanResult
import com.link.riderservice.api.WifiStatus
import com.link.riderservice.api.dto.WifiConnectionMode
import com.link.riderservice.api.exception.SPRiderServicesException
import com.link.riderservice.core.logging.logD
import com.link.riderservice.core.logging.logE
import com.link.riderservice.data.local.ConfigPreferences
import com.link.riderservice.feature.analytics.ConnectionAnalytics
import com.link.riderservice.feature.connection.wifi.callback.IP2pListener
import com.link.riderservice.feature.connection.wifi.callback.IWiFiAPListener

internal class WifiConnectionManager(
    private val connectionMode: WifiConnectionMode,
    private val callback: WifiConnectionCallback
) {
    private val TAG = WifiConnectionManager::class.java.simpleName
    
    // WiFi AP 相关
    private var wifiAPManager: WiFiAPManager? = null
    private var candidateApSsid: String? = null
    private var candidateApPassword: String? = null
    private var tempApSsid: String? = null
    private var tempApPassword: String? = null

    // 当前连接的 AP 凭据（用于保存到配置）
    private var currentConnectedApSsid: String? = null
    private var currentConnectedApPassword: String? = null
    
    // P2P 相关
    private var p2pManager: P2pManager? = null
    private var isP2pConnected = false
    
    /**
     * WiFi 连接回调接口
     */
    interface WifiConnectionCallback {
        fun onWifiStatusChanged(status: WifiStatus)
        fun onWifiConnected(ssid: String, ipAddress: String)
        fun onWifiDisconnected()
        fun onWifiConnectionFailed(reason: Int)
        fun onNetworkScanComplete(networks: List<ScanResult>)
        fun onWifiStateChanged(enabled: Boolean)
        fun onExceptionOccurred(exception: SPRiderServicesException)
        fun onRequestWifiInfo(isReset: Boolean = true)
        fun onP2pConnectionEstablished()
        fun onP2pConnectionExist(message: String)
    }

    /**
     * 初始化 WiFi 管理器
     */
    fun initialize(context: Context) {
        when (connectionMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                initializeAPManager(context)
            }
            WifiConnectionMode.WIFI_P2P -> {
                initializeP2pManager(context)
            }
        }
    }

    /**
     * 初始化 AP 管理器
     */
    private fun initializeAPManager(context: Context) {
        wifiAPManager = WiFiAPManager(object : IWiFiAPListener {
            override fun onWifiConnecting(ssid: String) {
                logD(TAG, "WiFi connecting to: $ssid")
            }

            override fun onWifiConnected(ssid: String, ipAddress: String) {
                logD(TAG, "WiFi connected: $ssid, IP: $ipAddress")
                callback.onWifiStatusChanged(WifiStatus.DeviceConnected)
                callback.onWifiConnected(ssid, ipAddress)
            }

            override fun onWifiDisconnected() {
                logD(TAG, "WiFi disconnected")
                callback.onWifiStatusChanged(WifiStatus.DeviceDisconnected)
                callback.onWifiDisconnected()
            }

            override fun onWifiConnectionFailed(reason: Int) {
                logE(TAG, "WiFi connection failed: $reason")
                ConnectionAnalytics.finishConnection(false, "WiFi connection failed: reason $reason")
                callback.onWifiStatusChanged(WifiStatus.DeviceDisconnected)
                callback.onWifiConnectionFailed(reason)
            }

            override fun onNetworkScanComplete(networks: List<ScanResult>) {
                logD(TAG, "Network scan completed, found ${networks.size} networks")
                callback.onNetworkScanComplete(networks)
            }

            override fun requestWifiInfo() {
                callback.onRequestWifiInfo(false)
            }

            override fun onWifiState(opened: Boolean) {
                logD(TAG, "WiFi state changed: $opened")
                callback.onWifiStateChanged(opened)
            }
        })
    }

    /**
     * 初始化 P2P 管理器
     */
    private fun initializeP2pManager(context: Context) {
        p2pManager = P2pManager(object : IP2pListener {
            override fun requestWifiInfo() {
                callback.onRequestWifiInfo()
            }

            override fun onWifiState(opened: Boolean) {
                callback.onWifiStateChanged(opened)
            }

            override fun onCancelConnect() {
                logD(TAG, "P2P onCancelConnect")
                ConnectionAnalytics.finishConnection(false, "P2P connection cancelled or failed")

                if (isP2pConnected) {
                    shutdown()
                } else {
                    callback.onRequestWifiInfo()
                }
            }

            override fun onWifiConnectSuccess() {
                handleP2pConnectionExist("P2P already connected")
            }

            override fun connectExist() {
                handleP2pConnectionExist("P2P connection exist")
            }

            override fun onP2pConnectSuccess() {
                logD(TAG, "P2P connect success")
                callback.onP2pConnectionEstablished()
            }

            override fun onWifiDisconnect() {
                logD(TAG, "P2P onWifiDisconnect")
                if (isP2pConnected) {
                    shutdown()
                }
                isP2pConnected = false
                callback.onWifiDisconnected()
            }
        })
    }

    /**
     * 连接到仪表端WiFi热点（AP客户端模式）
     */
    fun connectToInstrumentWifi(ssid: String, password: String) {
        when (connectionMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                logD(TAG, "Start connecting to instrument Wi-Fi: $ssid (AP Mode)")
                candidateApSsid = ssid
                candidateApPassword = password
                wifiAPManager?.connectToWifi(ssid, password)
            }
            WifiConnectionMode.WIFI_P2P -> {
                logE(TAG, "Cannot connect to WiFi in P2P mode")
            }
        }
    }

    /**
     * 搜索并连接 WLAN 直连 (P2P模式)
     */
    fun startSearchWifiAndConnect(context: Context, address: String, port: Int) {
        when (connectionMode) {
            WifiConnectionMode.WIFI_P2P -> {
                ConnectionAnalytics.startWifiScan()
                val configPrefs = ConfigPreferences.getInstance(context)
                configPrefs.setWifiAddress(address)
                configPrefs.setWifiPort(port)

                logD(TAG, "P2P connect address:$address port:$port")
                p2pManager?.start(address, port)
            }
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                logE(TAG, "Cannot start P2P search in AP mode")
            }
        }
    }

    /**
     * 断开WiFi连接
     */
    fun disconnect() {
        when (connectionMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                logD(TAG, "WiFi AP client disconnected")
            }
            WifiConnectionMode.WIFI_P2P -> {
                p2pManager?.stop()
                isP2pConnected = false
                logD(TAG, "WiFi P2P connection disconnected")
            }
        }
    }

    /**
     * 重置WiFi连接状态
     */
    fun resetConnectionState() {
        logD(TAG, "Resetting WiFi connection state")
        when (connectionMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                wifiAPManager?.resetConnectionState()
            }
            WifiConnectionMode.WIFI_P2P -> {
                p2pManager?.resetConnectionState()
            }
        }
        callback.onWifiStatusChanged(WifiStatus.IDLE)
    }

    /**
     * 获取当前WiFi连接状态信息
     */
    fun getConnectionInfo(): String {
        return when (connectionMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                val state = wifiAPManager?.getCurrentState()
                val attempts = wifiAPManager?.getReconnectionAttempts()
                "AP Mode - State: $state, Reconnection attempts: $attempts"
            }
            WifiConnectionMode.WIFI_P2P -> {
                p2pManager?.getConnectionInfo() ?: "P2P Mode - No connection info"
            }
        }
    }

    /**
     * 检查WiFi权限
     */
    fun checkPermissions(): Boolean {
        return when (connectionMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                wifiAPManager?.checkPermissions() ?: false
            }
            WifiConnectionMode.WIFI_P2P -> {
                true // P2P模式的权限检查逻辑
            }
        }
    }

    /**
     * 检查WiFi是否启用
     */
    fun isWifiEnabled(): Boolean {
        return when (connectionMode) {
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                wifiAPManager?.isWifiEnabled() ?: false
            }
            WifiConnectionMode.WIFI_P2P -> {
                p2pManager?.isWifiEnabled() ?: false
            }
        }
    }

    /**
     * 处理P2P连接已存在的通用逻辑
     */
    private fun handleP2pConnectionExist(logMessage: String) {
        logD(TAG, logMessage)
        ConnectionAnalytics.endWifiScan()
        ConnectionAnalytics.startWifiConnect()
        ConnectionAnalytics.endWifiConnect()

        isP2pConnected = true
        callback.onP2pConnectionExist(logMessage)
    }

    /**
     * 关闭连接（用于P2P模式）
     */
    private fun shutdown() {
        when (connectionMode) {
            WifiConnectionMode.WIFI_P2P -> {
                logD(TAG, "Shutting down P2P connection")
                p2pManager?.stop()
                isP2pConnected = false
            }
            WifiConnectionMode.WIFI_AP_CLIENT -> {
                logD(TAG, "Shutting down AP connection")
            }
        }
    }

    /**
     * 设置潜在的AP凭据
     */
    fun setPotentialApCredentials(ssid: String, password: String) {
        tempApSsid = ssid
        tempApPassword = password
    }

    /**
     * 获取候选AP凭据
     */
    fun getCandidateApCredentials(): Pair<String?, String?> {
        return Pair(candidateApSsid, candidateApPassword)
    }

    /**
     * 清除候选AP凭据
     */
    fun clearCandidateApCredentials() {
        candidateApSsid = null
        candidateApPassword = null
    }

    /**
     * 获取 P2P 连接状态
     */
    fun isP2pConnected(): Boolean {
        return if (connectionMode == WifiConnectionMode.WIFI_P2P) {
            isP2pConnected
        } else {
            false
        }
    }

    /**
     * 设置当前连接的 AP 凭据（连接成功时调用）
     */
    fun setCurrentConnectedApCredentials(ssid: String, password: String) {
        if (connectionMode == WifiConnectionMode.WIFI_AP_CLIENT) {
            currentConnectedApSsid = ssid
            currentConnectedApPassword = password
        }
    }

    /**
     * 获取当前连接的 AP 凭据
     */
    fun getCurrentConnectedApCredentials(): Pair<String?, String?> {
        return Pair(currentConnectedApSsid, currentConnectedApPassword)
    }

    /**
     * 清除当前连接的 AP 凭据
     */
    fun clearCurrentConnectedApCredentials() {
        currentConnectedApSsid = null
        currentConnectedApPassword = null
    }
}
