package com.link.riderservice.feature.cast.autolink.project

/**
 * <AUTHOR>
 * @date 2017/9/19
 */
internal class VideoSource(
    var videoSourceListener: VideoSourceCallbacks, private val autoStartProjection: Boolean,
    private val remoteHost: String?
) : NativeObject {
    override val nativeInstance: Long = 0
    fun create(id: Int, nativeObjectInstance: Long): Boolean {
        return nativeInit(id, nativeObjectInstance, autoStartProjection, remoteHost) == 0
    }

    override fun destroy() {
        nativeShutdown()
    }

    @Throws(IllegalStateException::class)
    private external fun nativeInit(
        id: Int, nativeObjectInstance: Long, autoStartProjection: Boolean,
        remoteHostAddress: String?
    ): Int

    private external fun nativeShutdown()
    external fun sendSetup(setupType: Int)
    external fun sendStart(configurationIndex: Int, sessionId: Int, width: Int, height: Int)
    external fun sendStop()
    external fun sendData(timestamp: Long, data: ByteArray?, dataLength: Int, flags: Int)
    external fun sendVideoFocusRequestNotifi(channelId: Int, mode: Int, reason: Int)
    external fun sendDisplayAreaChangeResponse()
    external fun sendVideoOrientation(landscapeOrientation: Boolean)
    external fun isStartResponseMessageExist(): Boolean
}